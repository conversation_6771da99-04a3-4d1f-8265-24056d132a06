<!DOCTYPE html>
<html lang="en" data-layout="vertical" data-topbar="light" data-sidebar="dark" data-sidebar-size="lg" data-sidebar-image="none" data-preloader="disable" data-theme="default" data-theme-colors="default" data-bs-theme="light">

<!-- Mirrored from themesbrand.com/velzon/html/master/index.html by HTTrack Website Copier/3.x [XR&CO'2014], Fri, 23 Aug 2024 16:35:21 GMT -->

<head>

    <meta charset="utf-8" />
    <title><?php echo $__env->yieldContent('title', 'Dashboard Admission Pendaftaran Online'); ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="Premium Multipurpose Admin & Dashboard Template" name="description" />
    <meta content="Themesbrand" name="author" />
    <!-- App favicon -->
    <link rel="shortcut icon" href="<?php echo e(asset('assets/images/favicon.ico')); ?>">
    <link href="<?php echo e(asset('assets/libs/jsvectormap/css/jsvectormap.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/libs/swiper/swiper-bundle.min.css')); ?>" rel="stylesheet" type="text/css" />
    <script src="<?php echo e(asset('assets/js/layout.js')); ?>"></script>
    <link href="<?php echo e(asset('assets/css/bootstrap.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/icons.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/app.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/custom.min.css')); ?>" rel="stylesheet" type="text/css" />
    <!-- Font Awesome dari CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" />
    
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet" type="text/css" />
    <link href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap5.min.css" rel="stylesheet" type="text/css" />
    
    <!-- jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap5.min.js"></script>
    
    <?php echo $__env->yieldPushContent('styles'); ?>

    <!-- Custom CSS untuk Hamburger Menu -->
    <style>
        /* Hamburger Icon Animation */
        .hamburger-icon {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            width: 20px;
            height: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .hamburger-icon span {
            display: block;
            height: 2px;
            width: 100%;
            background-color: currentColor;
            transition: all 0.3s ease;
            transform-origin: center;
        }

        .hamburger-icon.open span:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }

        .hamburger-icon.open span:nth-child(2) {
            opacity: 0;
        }

        .hamburger-icon.open span:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -6px);
        }

        /* Sidebar Toggle Animation */
        .app-menu {
            transition: all 0.3s ease;
        }

        /* Mobile: Show/Hide sidebar */
        @media (max-width: 767px) {
            .app-menu {
                position: fixed;
                left: -250px;
                z-index: 1000;
                transition: left 0.3s ease;
            }

            .vertical-sidebar-enable .app-menu {
                left: 0;
            }

            .vertical-overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 999;
            }

            .vertical-sidebar-enable .vertical-overlay {
                display: block;
            }
        }

        /* Desktop: Sidebar size toggle */
        @media (min-width: 768px) {
            [data-sidebar-size="sm"] .app-menu {
                width: 70px;
            }

            [data-sidebar-size="sm"] .app-menu .navbar-nav .nav-link span {
                display: none;
            }

            [data-sidebar-size="lg"] .app-menu {
                width: 250px;
            }
        }
    </style>
</head>
<style>
    .navbar-brand-box{
        margin-top: 1rem;
    }
    /* Perbaikan style DataTables */
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.3em 0.8em;
        margin-left: 2px;
        border-radius: 4px;
    }
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #405189;
        color: white !important;
        border: 1px solid #405189;
    }
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f6f9;
        color: #405189 !important;
        border: 1px solid #f3f6f9;
    }
    .dataTables_wrapper .dataTables_length, 
    .dataTables_wrapper .dataTables_filter, 
    .dataTables_wrapper .dataTables_info, 
    .dataTables_wrapper .dataTables_processing, 
    .dataTables_wrapper .dataTables_paginate {
        margin-bottom: 10px;
        color: #495057;
    }
</style>

<body>
    <!-- Begin page -->
    <div id="layout-wrapper">

        <?php echo $__env->make('layouts.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('layouts.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <!-- Content Wrapper. Contains page content -->
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <?php echo $__env->yieldContent('content'); ?>
                </div>
            </div>
            
            <?php echo $__env->make('layouts.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    </div>

    <!-- JAVASCRIPT -->
    <script src="<?php echo e(asset('assets/libs/bootstrap/js/bootstrap.bundle.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/simplebar/simplebar.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/node-waves/waves.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/feather-icons/feather.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/pages/plugins/lord-icon-2.1.0.js')); ?>"></script>

    <!-- Load Flatpickr before plugins.js -->
    <script src="<?php echo e(asset('assets/libs/flatpickr/flatpickr.min.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/js/plugins.js')); ?>"></script>

    <!-- App JS -->
    <script src="<?php echo e(asset('assets/js/app.js')); ?>"></script>

    <!-- Fix CSS untuk Dropdown User -->
    <style>
        .dropdown-menu {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            z-index: 1000;
            min-width: 10rem;
            padding: 0.5rem 0;
            margin: 0;
            font-size: 0.875rem;
            color: var(--vz-body-color);
            text-align: left;
            background-color: var(--vz-dropdown-bg, #fff);
            background-clip: padding-box;
            border: 1px solid var(--vz-dropdown-border-color, rgba(0,0,0,.15));
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .dropdown-menu.show {
            display: block !important;
        }

        .dropdown-menu-end {
            right: 0;
            left: auto;
        }

        .dropdown-item {
            display: block;
            width: 100%;
            padding: 0.375rem 1rem;
            clear: both;
            font-weight: 400;
            color: var(--vz-dropdown-link-color, #212529);
            text-align: inherit;
            text-decoration: none;
            white-space: nowrap;
            background-color: transparent;
            border: 0;
        }

        .dropdown-item:hover,
        .dropdown-item:focus {
            color: var(--vz-dropdown-link-hover-color, #1e2125);
            background-color: var(--vz-dropdown-link-hover-bg, #e9ecef);
        }

        .dropdown-header {
            display: block;
            padding: 0.5rem 1rem;
            margin-bottom: 0;
            font-size: 0.75rem;
            color: var(--vz-dropdown-header-color, #6c757d);
            white-space: nowrap;
        }

        .dropdown-divider {
            height: 0;
            margin: 0.5rem 0;
            overflow: hidden;
            border-top: 1px solid var(--vz-dropdown-divider-bg, rgba(0,0,0,.15));
        }
    </style>

    <!-- Fix untuk Dropdown User dan Hamburger Menu -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // === DROPDOWN USER FUNCTIONALITY ===
            // Pastikan Bootstrap dropdown ter-inisialisasi
            var dropdownElementList = [].slice.call(document.querySelectorAll('[data-bs-toggle="dropdown"]'));
            var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });

            // Debug: Log jika dropdown ditemukan
            console.log('Dropdown elements found:', dropdownElementList.length);

            // Fallback: Manual click handler untuk user dropdown
            const userDropdownBtn = document.getElementById('page-header-user-dropdown');
            const userDropdownMenu = document.querySelector('.topbar-user .dropdown-menu');

            if (userDropdownBtn && userDropdownMenu) {
                userDropdownBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Toggle dropdown menu
                    if (userDropdownMenu.classList.contains('show')) {
                        userDropdownMenu.classList.remove('show');
                        userDropdownBtn.setAttribute('aria-expanded', 'false');
                    } else {
                        // Tutup dropdown lain yang mungkin terbuka
                        document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                            menu.classList.remove('show');
                        });

                        userDropdownMenu.classList.add('show');
                        userDropdownBtn.setAttribute('aria-expanded', 'true');
                    }
                });

                // Tutup dropdown ketika klik di luar
                document.addEventListener('click', function(e) {
                    if (!userDropdownBtn.contains(e.target) && !userDropdownMenu.contains(e.target)) {
                        userDropdownMenu.classList.remove('show');
                        userDropdownBtn.setAttribute('aria-expanded', 'false');
                    }
                });

                console.log('User dropdown manual handler initialized');
            }

            // === HAMBURGER MENU FUNCTIONALITY ===
            // Wait for app.js to load first, then add our custom handler
            setTimeout(function() {
                const hamburgerBtn = document.getElementById('topnav-hamburger-icon');
                if (hamburgerBtn) {
                    // Remove existing event listeners to avoid conflicts
                    const newHamburgerBtn = hamburgerBtn.cloneNode(true);
                    hamburgerBtn.parentNode.replaceChild(newHamburgerBtn, hamburgerBtn);

                    // Add our custom event listener
                    newHamburgerBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('Custom Hamburger clicked!'); // Debug

                        // Toggle sidebar using the same logic as app.js
                        const body = document.body;
                        const hamburgerIcon = document.querySelector('.hamburger-icon');
                        const screenWidth = window.innerWidth;

                        // Toggle hamburger icon animation first
                        if (hamburgerIcon) {
                            hamburgerIcon.classList.toggle('open');
                        }

                        if (screenWidth <= 767) {
                            // Mobile: Toggle sidebar visibility
                            body.classList.toggle('vertical-sidebar-enable');
                        } else {
                            // Desktop/Tablet: Toggle sidebar size
                            const currentSize = document.documentElement.getAttribute('data-sidebar-size');
                            if (currentSize === 'sm' || currentSize === 'sm-hover') {
                                document.documentElement.setAttribute('data-sidebar-size', 'lg');
                            } else {
                                document.documentElement.setAttribute('data-sidebar-size', 'sm');
                            }
                        }
                    });

                    console.log('Custom Hamburger menu handler initialized');
                }
            }, 100); // Wait 100ms for app.js to initialize
        });
    </script>

    <!-- apexcharts -->
    <script src="<?php echo e(asset('assets/libs/apexcharts/apexcharts.min.js')); ?>"></script>

    <!-- Vector map-->
    <script src="<?php echo e(asset('assets/libs/jsvectormap/js/jsvectormap.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/jsvectormap/maps/world-merc.js')); ?>"></script>

    <!--Swiper slider js-->
    <script src="<?php echo e(asset('assets/libs/swiper/swiper-bundle.min.js')); ?>"></script>

    <!-- Dashboard init -->
    <script src="<?php echo e(asset('assets/js/pages/dashboard-ecommerce.init.js')); ?>"></script>

    <!-- App js -->
    <script src="<?php echo e(asset('assets/js/app.js')); ?>"></script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>

<!-- Mirrored from themesbrand.com/velzon/html/master/index.html by HTTrack Website Copier/3.x [XR&CO'2014], Fri, 23 Aug 2024 16:36:17 GMT -->

</html>
<?php /**PATH C:\laragon\www\regkan\resources\views/layouts/app.blade.php ENDPATH**/ ?>